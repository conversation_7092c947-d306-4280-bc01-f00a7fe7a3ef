// GoMyHire司机FAQ系统主应用
class FAQApp {
    constructor() {
        this.i18n = new I18nManager();
        this.dataManager = new DataManager();
        this.currentPage = 'welcome';
        this.currentQuestion = null;
        this.favorites = JSON.parse(localStorage.getItem('faq-favorites') || '[]');
        this.recentQuestions = JSON.parse(localStorage.getItem('faq-recent') || '[]');
        
        this.init();
    }
    
    // 初始化应用
    init() {
        this.setupEventListeners();
        this.renderCategories();
        this.updateFavoritesCount();
        this.updateRecentCount();
        this.showWelcomePage();
        this.setupBackToTop();
        this.i18n.updatePageTexts();
        
        // 处理URL参数
        this.handleUrlParams();
    }
    
    // 设置事件监听器
    setupEventListeners() {
        // 语言切换
        document.querySelectorAll('.lang-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const lang = e.target.dataset.lang;
                this.switchLanguage(lang);
            });
        });
        
        // 搜索功能
        const searchInput = document.getElementById('searchInput');
        const searchBtn = document.getElementById('searchBtn');
        
        searchBtn.addEventListener('click', () => this.performSearch());
        searchInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.performSearch();
            }
        });
        
        // 侧边栏切换（移动端）
        document.getElementById('toggleSidebar').addEventListener('click', () => {
            const nav = document.getElementById('categoryNav');
            nav.classList.toggle('show');
        });
        
        // 快速访问按钮
        document.querySelectorAll('.quick-link').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const action = e.currentTarget.dataset.action;
                this.handleQuickAccess(action);
            });
        });
        
        // 快速开始按钮
        document.querySelectorAll('.quick-start-buttons .btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const category = e.currentTarget.dataset.category;
                if (category) {
                    this.showCategoryPage(category);
                }
            });
        });
        
        // FAQ页面操作按钮
        document.getElementById('favoriteBtn').addEventListener('click', () => {
            this.toggleFavorite();
        });
        
        document.getElementById('shareBtn').addEventListener('click', () => {
            this.shareQuestion();
        });
        
        document.getElementById('feedbackBtn').addEventListener('click', () => {
            this.showFeedback();
        });
    }
    
    // 切换语言
    switchLanguage(lang) {
        // 更新按钮状态
        document.querySelectorAll('.lang-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-lang="${lang}"]`).classList.add('active');
        
        // 设置语言
        this.i18n.setLanguage(lang);
        
        // 重新渲染当前页面
        this.refreshCurrentPage();
    }
    
    // 执行搜索
    performSearch() {
        const query = document.getElementById('searchInput').value.trim();
        if (!query) return;
        
        this.showLoadingOverlay(true);
        
        setTimeout(() => {
            const results = this.dataManager.searchQuestions(query, this.i18n.getCurrentLanguage());
            this.showSearchResults(query, results);
            this.showLoadingOverlay(false);
        }, 300);
    }
    
    // 显示搜索结果
    showSearchResults(query, results) {
        this.currentPage = 'search';
        this.hideAllPages();
        
        const searchPage = document.getElementById('searchPage');
        const searchInfo = document.getElementById('searchInfo');
        const searchResults = document.getElementById('searchResults');
        
        // 更新搜索信息
        const count = results.length;
        searchInfo.textContent = this.i18n.t('searchResultsCount', { count });
        
        // 渲染搜索结果
        if (results.length === 0) {
            searchResults.innerHTML = `
                <div class="no-results">
                    <h3>${this.i18n.t('searchNoResults')}</h3>
                    <p>请尝试使用其他关键词或浏览分类查找相关问题。</p>
                </div>
            `;
        } else {
            searchResults.innerHTML = results.map(result => this.renderSearchResultItem(result)).join('');
            
            // 添加点击事件
            searchResults.querySelectorAll('.search-result-item').forEach(item => {
                item.addEventListener('click', () => {
                    const questionId = item.dataset.questionId;
                    this.showQuestion(questionId);
                });
            });
        }
        
        searchPage.classList.remove('page-hidden');
    }
    
    // 渲染搜索结果项
    renderSearchResultItem(result) {
        const question = result.question;
        const lang = this.i18n.getCurrentLanguage();
        const category = this.dataManager.getCategories()[question.category];
        
        // 生成摘要
        const excerpt = this.generateExcerpt(question.content[lang], 150);
        
        return `
            <div class="search-result-item" data-question-id="${question.id}">
                <div class="search-result-header">
                    <span class="search-result-id">${question.id}</span>
                    <h3 class="search-result-title">${question.title[lang]}</h3>
                    <span class="search-result-category">${category.name[lang]}</span>
                </div>
                <div class="search-result-excerpt">${excerpt}</div>
            </div>
        `;
    }
    
    // 生成内容摘要
    generateExcerpt(content, maxLength) {
        const text = content.replace(/<[^>]*>/g, '').replace(/\s+/g, ' ').trim();
        return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
    }
    
    // 渲染分类导航
    renderCategories() {
        const categoryNav = document.getElementById('categoryNav');
        const categories = this.dataManager.getCategories();
        const lang = this.i18n.getCurrentLanguage();
        
        const categoryItems = Object.values(categories)
            .sort((a, b) => a.priority - b.priority)
            .map(category => {
                const questionCount = this.dataManager.getQuestionsByCategory(category.id).length;
                return `
                    <div class="category-item">
                        <a href="#" class="category-link" data-category="${category.id}">
                            <span class="category-icon">${category.icon}</span>
                            <span class="category-name">${category.name[lang]}</span>
                            <span class="category-count">${questionCount}</span>
                        </a>
                    </div>
                `;
            }).join('');
        
        categoryNav.innerHTML = categoryItems;
        
        // 添加点击事件
        categoryNav.querySelectorAll('.category-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const categoryId = e.currentTarget.dataset.category;
                this.showCategoryPage(categoryId);
            });
        });
    }
    
    // 显示分类页面
    showCategoryPage(categoryId) {
        this.currentPage = 'category';
        this.hideAllPages();
        
        const categoryPage = document.getElementById('categoryPage');
        const categoryTitle = document.getElementById('categoryTitle');
        const categoryDescription = document.getElementById('categoryDescription');
        const categoryQuestions = document.getElementById('categoryQuestions');
        
        const category = this.dataManager.getCategories()[categoryId];
        const questions = this.dataManager.getQuestionsByCategory(categoryId);
        const lang = this.i18n.getCurrentLanguage();
        
        // 更新分类信息
        categoryTitle.textContent = category.name[lang];
        categoryDescription.textContent = category.description[lang];
        
        // 渲染问题列表
        categoryQuestions.innerHTML = questions.map(question => this.renderQuestionItem(question)).join('');
        
        // 添加点击事件
        categoryQuestions.querySelectorAll('.question-item').forEach(item => {
            item.addEventListener('click', () => {
                const questionId = item.dataset.questionId;
                this.showQuestion(questionId);
            });
        });
        
        categoryPage.classList.remove('page-hidden');
    }
    
    // 渲染问题项
    renderQuestionItem(question) {
        const lang = this.i18n.getCurrentLanguage();
        const summary = this.generateExcerpt(question.content[lang], 100);
        
        return `
            <div class="question-item" data-question-id="${question.id}">
                <div class="question-header">
                    <span class="question-id">${question.id}</span>
                    <h3 class="question-title">${question.title[lang]}</h3>
                    <span class="question-priority priority-${question.priority}">
                        ${this.i18n.t('priority' + question.priority.charAt(0).toUpperCase() + question.priority.slice(1))}
                    </span>
                </div>
                <div class="question-summary">${summary}</div>
            </div>
        `;
    }
    
    // 显示问题详情
    showQuestion(questionId) {
        const question = this.dataManager.getQuestionById(questionId);
        if (!question) return;
        
        this.currentQuestion = question;
        this.currentPage = 'faq';
        this.addToRecent(questionId);
        this.hideAllPages();
        
        const faqPage = document.getElementById('faqPage');
        const breadcrumb = document.getElementById('breadcrumb');
        const faqContent = document.getElementById('faqContent');
        const relatedQuestions = document.getElementById('relatedQuestions');
        
        const lang = this.i18n.getCurrentLanguage();
        const category = this.dataManager.getCategories()[question.category];
        
        // 更新面包屑
        breadcrumb.innerHTML = `
            <a href="#" onclick="app.showWelcomePage()">${this.i18n.t('welcomeTitle')}</a>
            <span class="breadcrumb-separator">></span>
            <a href="#" onclick="app.showCategoryPage('${question.category}')">${category.name[lang]}</a>
            <span class="breadcrumb-separator">></span>
            <span>${question.title[lang]}</span>
        `;
        
        // 更新FAQ内容
        faqContent.innerHTML = `
            <div class="faq-header">
                <span class="faq-id">${question.id}</span>
                <h1 class="faq-title">
                    ${question.title[lang]}
                    <span class="faq-priority priority-${question.priority}">
                        ${this.i18n.t('priority' + question.priority.charAt(0).toUpperCase() + question.priority.slice(1))}
                    </span>
                </h1>
            </div>
            <div class="faq-body">
                ${question.content[lang]}
            </div>
        `;
        
        // 更新相关问题
        this.renderRelatedQuestions(relatedQuestions, question);
        
        // 更新收藏按钮状态
        this.updateFavoriteButton();
        
        faqPage.classList.remove('page-hidden');
    }
    
    // 渲染相关问题
    renderRelatedQuestions(container, question) {
        const relatedQuestions = this.dataManager.getRelatedQuestions(question.id);
        const lang = this.i18n.getCurrentLanguage();
        
        if (relatedQuestions.length === 0) {
            container.classList.add('page-hidden');
            return;
        }
        
        container.innerHTML = `
            <h3>${this.i18n.t('relatedQuestions')}</h3>
            <div class="related-list">
                ${relatedQuestions.map(q => `
                    <a href="#" class="related-item" data-question-id="${q.id}">
                        <span class="related-item-id">${q.id}</span>
                        <span class="related-item-title">${q.title[lang]}</span>
                    </a>
                `).join('')}
            </div>
        `;
        
        // 添加点击事件
        container.querySelectorAll('.related-item').forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                const questionId = e.currentTarget.dataset.questionId;
                this.showQuestion(questionId);
            });
        });
        
        container.classList.remove('page-hidden');
    }
    
    // 显示欢迎页面
    showWelcomePage() {
        this.currentPage = 'welcome';
        this.hideAllPages();
        document.getElementById('welcomePage').classList.remove('page-hidden');
    }

    // 隐藏所有页面
    hideAllPages() {
        document.getElementById('welcomePage').classList.add('page-hidden');
        document.getElementById('faqPage').classList.add('page-hidden');
        document.getElementById('searchPage').classList.add('page-hidden');
        document.getElementById('categoryPage').classList.add('page-hidden');
    }
    
    // 刷新当前页面
    refreshCurrentPage() {
        this.renderCategories();
        
        switch (this.currentPage) {
            case 'welcome':
                this.showWelcomePage();
                break;
            case 'faq':
                if (this.currentQuestion) {
                    this.showQuestion(this.currentQuestion.id);
                }
                break;
            case 'search':
                // 重新执行搜索
                this.performSearch();
                break;
            case 'category':
                // 需要保存当前分类ID
                break;
        }
    }
    
    // 处理URL参数
    handleUrlParams() {
        const urlParams = new URLSearchParams(window.location.search);
        const questionId = urlParams.get('q');
        const category = urlParams.get('c');
        const search = urlParams.get('s');
        
        if (questionId) {
            this.showQuestion(questionId);
        } else if (category) {
            this.showCategoryPage(category);
        } else if (search) {
            document.getElementById('searchInput').value = search;
            this.performSearch();
        }
    }
    
    // 显示加载遮罩
    showLoadingOverlay(show) {
        const overlay = document.getElementById('loadingOverlay');
        if (show) {
            overlay.classList.add('show');
        } else {
            overlay.classList.remove('show');
        }
    }
    
    // 设置返回顶部按钮
    setupBackToTop() {
        const backToTopBtn = document.getElementById('backToTop');

        window.addEventListener('scroll', () => {
            if (window.pageYOffset > 300) {
                backToTopBtn.classList.add('show');
            } else {
                backToTopBtn.classList.remove('show');
            }
        });

        backToTopBtn.addEventListener('click', () => {
            window.scrollTo({ top: 0, behavior: 'smooth' });
        });
    }

    // 处理快速访问
    handleQuickAccess(action) {
        switch (action) {
            case 'favorites':
                this.showFavorites();
                break;
            case 'recent':
                this.showRecent();
                break;
            case 'popular':
                this.showPopular();
                break;
        }
    }

    // 显示收藏夹
    showFavorites() {
        if (this.favorites.length === 0) {
            this.showNotification('您还没有收藏任何问题');
            return;
        }

        const questions = this.favorites
            .map(id => this.dataManager.getQuestionById(id))
            .filter(q => q !== undefined);

        this.showQuestionList('收藏夹', questions);
    }

    // 显示最近浏览
    showRecent() {
        if (this.recentQuestions.length === 0) {
            this.showNotification('您还没有浏览任何问题');
            return;
        }

        const questions = this.recentQuestions
            .map(id => this.dataManager.getQuestionById(id))
            .filter(q => q !== undefined);

        this.showQuestionList('最近浏览', questions);
    }

    // 显示热门问题
    showPopular() {
        const questions = this.dataManager.getPopularQuestions();
        this.showQuestionList('热门问题', questions);
    }

    // 显示问题列表
    showQuestionList(title, questions) {
        this.currentPage = 'category';
        this.hideAllPages();

        const categoryPage = document.getElementById('categoryPage');
        const categoryTitle = document.getElementById('categoryTitle');
        const categoryDescription = document.getElementById('categoryDescription');
        const categoryQuestions = document.getElementById('categoryQuestions');

        categoryTitle.textContent = title;
        categoryDescription.textContent = `共 ${questions.length} 个问题`;

        categoryQuestions.innerHTML = questions.map(question => this.renderQuestionItem(question)).join('');

        // 添加点击事件
        categoryQuestions.querySelectorAll('.question-item').forEach(item => {
            item.addEventListener('click', () => {
                const questionId = item.dataset.questionId;
                this.showQuestion(questionId);
            });
        });

        categoryPage.classList.remove('page-hidden');
    }

    // 切换收藏状态
    toggleFavorite() {
        if (!this.currentQuestion) return;

        const questionId = this.currentQuestion.id;
        const index = this.favorites.indexOf(questionId);

        if (index === -1) {
            // 添加到收藏
            this.favorites.push(questionId);
            this.showNotification(this.i18n.t('addedToFavorites'));
        } else {
            // 从收藏中移除
            this.favorites.splice(index, 1);
            this.showNotification(this.i18n.t('removedFromFavorites'));
        }

        // 保存到本地存储
        localStorage.setItem('faq-favorites', JSON.stringify(this.favorites));

        // 更新按钮状态和计数
        this.updateFavoriteButton();
        this.updateFavoritesCount();
    }

    // 更新收藏按钮状态
    updateFavoriteButton() {
        if (!this.currentQuestion) return;

        const favoriteBtn = document.getElementById('favoriteBtn');
        const isFavorited = this.favorites.includes(this.currentQuestion.id);

        const icon = favoriteBtn.querySelector('.icon');
        const text = favoriteBtn.querySelector('span:last-child');

        if (isFavorited) {
            icon.textContent = '⭐';
            text.textContent = this.i18n.t('removeFromFavorites');
            favoriteBtn.classList.add('favorited');
        } else {
            icon.textContent = '☆';
            text.textContent = this.i18n.t('addToFavorites');
            favoriteBtn.classList.remove('favorited');
        }
    }

    // 更新收藏夹计数
    updateFavoritesCount() {
        const favCount = document.getElementById('favCount');
        favCount.textContent = this.favorites.length;
    }

    // 更新最近浏览计数
    updateRecentCount() {
        const recentCount = document.getElementById('recentCount');
        recentCount.textContent = this.recentQuestions.length;
    }

    // 添加到最近浏览
    addToRecent(questionId) {
        // 移除已存在的记录
        const index = this.recentQuestions.indexOf(questionId);
        if (index !== -1) {
            this.recentQuestions.splice(index, 1);
        }

        // 添加到开头
        this.recentQuestions.unshift(questionId);

        // 限制最大数量
        if (this.recentQuestions.length > 20) {
            this.recentQuestions = this.recentQuestions.slice(0, 20);
        }

        // 保存到本地存储
        localStorage.setItem('faq-recent', JSON.stringify(this.recentQuestions));

        // 更新计数
        this.updateRecentCount();
    }

    // 分享问题
    shareQuestion() {
        if (!this.currentQuestion) return;

        const url = `${window.location.origin}${window.location.pathname}?q=${this.currentQuestion.id}`;

        if (navigator.share) {
            // 使用原生分享API
            navigator.share({
                title: this.currentQuestion.title[this.i18n.getCurrentLanguage()],
                text: '来自GoMyHire司机FAQ',
                url: url
            });
        } else {
            // 复制到剪贴板
            navigator.clipboard.writeText(url).then(() => {
                this.showNotification(this.i18n.t('copiedToClipboard'));
            }).catch(() => {
                // 降级方案
                const textArea = document.createElement('textarea');
                textArea.value = url;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                this.showNotification(this.i18n.t('copiedToClipboard'));
            });
        }
    }

    // 显示反馈
    showFeedback() {
        const feedback = prompt('请输入您的反馈意见：');
        if (feedback && feedback.trim()) {
            // 这里可以发送反馈到服务器
            console.log('用户反馈:', feedback);
            this.showNotification(this.i18n.t('thankYouForFeedback'));
        }
    }

    // 显示通知
    showNotification(message, duration = 3000) {
        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = 'notification';
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--primary-color);
            color: white;
            padding: 12px 20px;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-lg);
            z-index: 1001;
            transform: translateX(100%);
            transition: transform 0.3s ease;
        `;

        document.body.appendChild(notification);

        // 显示动画
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);

        // 隐藏动画
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, duration);
    }
}

// 应用启动
let app;
document.addEventListener('DOMContentLoaded', () => {
    app = new FAQApp();
});
