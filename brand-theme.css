/* GoMyHire品牌主题样式 */

/* 品牌渐变 */
.brand-gradient-primary {
    background: linear-gradient(135deg, #1a73e8 0%, #4285f4 100%);
}

.brand-gradient-secondary {
    background: linear-gradient(135deg, #34a853 0%, #0f9d58 100%);
}

.brand-gradient-accent {
    background: linear-gradient(135deg, #fbbc04 0%, #ff9800 100%);
}

/* 品牌图标样式 */
.brand-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    border-radius: 12px;
    background: var(--primary-color);
    color: white;
    font-size: 24px;
    box-shadow: 0 4px 12px rgba(26, 115, 232, 0.3);
    transition: var(--transition);
}

.brand-icon:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(26, 115, 232, 0.4);
}

/* 品牌按钮样式 */
.btn-brand {
    background: linear-gradient(135deg, var(--primary-color) 0%, #4285f4 100%);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: var(--border-radius);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 4px 12px rgba(26, 115, 232, 0.3);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.btn-brand::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: var(--transition);
}

.btn-brand:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(26, 115, 232, 0.4);
}

.btn-brand:hover::before {
    left: 100%;
}

/* 品牌卡片样式 */
.brand-card {
    background: var(--surface-color);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.brand-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color), var(--warning-color));
    transform: scaleX(0);
    transition: var(--transition);
}

.brand-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.brand-card:hover::before {
    transform: scaleX(1);
}

/* 品牌徽章样式 */
.brand-badge {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 4px 12px;
    background: var(--primary-light);
    color: var(--primary-color);
    border-radius: 16px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.brand-badge.success {
    background: #e8f5e8;
    color: var(--success-color);
}

.brand-badge.warning {
    background: #fef7e0;
    color: var(--warning-color);
}

.brand-badge.danger {
    background: #fce8e6;
    color: var(--danger-color);
}

/* 品牌分割线 */
.brand-divider {
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--border-color), transparent);
    margin: 24px 0;
}

.brand-divider.thick {
    height: 2px;
    background: linear-gradient(90deg, transparent, var(--primary-color), transparent);
}

/* 品牌加载动画 */
.brand-loader {
    width: 40px;
    height: 40px;
    border: 3px solid var(--border-light);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: brand-spin 1s linear infinite;
}

@keyframes brand-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 品牌脉冲动画 */
.brand-pulse {
    animation: brand-pulse 2s ease-in-out infinite;
}

@keyframes brand-pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

/* 品牌悬浮动画 */
.brand-float {
    animation: brand-float 3s ease-in-out infinite;
}

@keyframes brand-float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* 品牌闪烁效果 */
.brand-shine {
    position: relative;
    overflow: hidden;
}

.brand-shine::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transform: rotate(45deg);
    animation: brand-shine 3s ease-in-out infinite;
}

@keyframes brand-shine {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    50% { transform: translateX(50%) translateY(50%) rotate(45deg); }
    100% { transform: translateX(200%) translateY(200%) rotate(45deg); }
}

/* 品牌状态指示器 */
.brand-status {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;
}

.brand-status.online {
    background: #e8f5e8;
    color: var(--success-color);
}

.brand-status.offline {
    background: #f5f5f5;
    color: var(--text-secondary);
}

.brand-status.busy {
    background: #fef7e0;
    color: var(--warning-color);
}

.brand-status::before {
    content: '';
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: currentColor;
}

.brand-status.online::before {
    animation: brand-pulse 2s ease-in-out infinite;
}

/* 品牌工具提示 */
.brand-tooltip {
    position: relative;
    cursor: help;
}

.brand-tooltip::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: var(--text-primary);
    color: white;
    padding: 8px 12px;
    border-radius: var(--border-radius);
    font-size: 0.75rem;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
    z-index: 1000;
}

.brand-tooltip::before {
    content: '';
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%) translateY(100%);
    border: 4px solid transparent;
    border-top-color: var(--text-primary);
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
}

.brand-tooltip:hover::after,
.brand-tooltip:hover::before {
    opacity: 1;
    visibility: visible;
}

/* 品牌进度条 */
.brand-progress {
    width: 100%;
    height: 8px;
    background: var(--border-light);
    border-radius: 4px;
    overflow: hidden;
}

.brand-progress-bar {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    border-radius: 4px;
    transition: width 0.3s ease;
    position: relative;
}

.brand-progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: brand-progress-shine 2s ease-in-out infinite;
}

@keyframes brand-progress-shine {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* 快速开始按钮中的小图标 */
.quick-start-buttons .brand-icon {
    width: 20px !important;
    height: 20px !important;
    font-size: 14px !important;
    margin-right: 8px !important;
    box-shadow: none !important;
}

/* 页面显示控制 */
.page-hidden {
    display: none !important;
}
