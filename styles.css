/* 基础样式重置和变量定义 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* GoMyHire品牌颜色 */
    --primary-color: #1a73e8;
    --primary-hover: #1557b0;
    --primary-light: #e8f0fe;
    --secondary-color: #5f6368;
    --accent-color: #34a853;
    --accent-hover: #2d8f47;
    --warning-color: #fbbc04;
    --danger-color: #ea4335;
    --success-color: #34a853;

    /* 背景和表面颜色 */
    --background-color: #f8f9fa;
    --surface-color: #ffffff;
    --surface-hover: #f1f3f4;
    --overlay-color: rgba(32, 33, 36, 0.6);

    /* 文本颜色 */
    --text-primary: #202124;
    --text-secondary: #5f6368;
    --text-tertiary: #80868b;
    --text-inverse: #ffffff;

    /* 边框和分割线 */
    --border-color: #dadce0;
    --border-light: #e8eaed;
    --divider-color: #f1f3f4;

    /* 阴影 */
    --shadow-sm: 0 1px 2px 0 rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);
    --shadow-md: 0 1px 2px 0 rgba(60, 64, 67, 0.3), 0 2px 6px 2px rgba(60, 64, 67, 0.15);
    --shadow-lg: 0 4px 8px 3px rgba(60, 64, 67, 0.15), 0 1px 3px rgba(60, 64, 67, 0.3);
    --shadow-xl: 0 8px 12px 6px rgba(60, 64, 67, 0.15), 0 4px 4px rgba(60, 64, 67, 0.3);

    /* 尺寸变量 */
    --header-height: 120px;
    --sidebar-width: 280px;
    --border-radius: 8px;
    --border-radius-lg: 12px;
    --transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
    --transition-fast: all 0.1s cubic-bezier(0.4, 0.0, 0.2, 1);
}

/* 基础样式 */
body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--background-color);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* 顶部导航栏 */
.header {
    background: var(--surface-color);
    border-bottom: 1px solid var(--border-color);
    position: sticky;
    top: 0;
    z-index: 100;
    box-shadow: var(--shadow-sm);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
}

.logo h1 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
}

/* 语言切换器 */
.language-switcher {
    display: flex;
    gap: 5px;
    background: var(--background-color);
    border-radius: var(--border-radius);
    padding: 4px;
}

.lang-btn {
    padding: 8px 16px;
    border: none;
    background: transparent;
    border-radius: calc(var(--border-radius) - 2px);
    cursor: pointer;
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-secondary);
    transition: var(--transition);
}

.lang-btn.active,
.lang-btn:hover {
    background: var(--surface-color);
    color: var(--primary-color);
    box-shadow: var(--shadow-sm);
}

/* 搜索栏 */
.search-container {
    display: flex;
    gap: 10px;
    margin-top: 15px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.search-input {
    flex: 1;
    padding: 12px 16px;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: var(--transition);
}

.search-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
}

.search-btn {
    padding: 12px 20px;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: 1.2rem;
    transition: var(--transition);
}

.search-btn:hover {
    background: var(--primary-hover);
}

/* 主要内容区域 */
.main-content {
    min-height: calc(100vh - var(--header-height) - 100px);
    padding: 30px 0;
}

.content-wrapper {
    display: grid;
    grid-template-columns: var(--sidebar-width) 1fr;
    gap: 30px;
    align-items: start;
}

/* 侧边栏 */
.sidebar {
    background: var(--surface-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    position: sticky;
    top: calc(var(--header-height) + 20px);
    max-height: calc(100vh - var(--header-height) - 40px);
    overflow-y: auto;
}

.sidebar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
}

.sidebar-header h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
}

.toggle-btn {
    display: none;
    background: none;
    border: none;
    font-size: 1.2rem;
    cursor: pointer;
    color: var(--text-secondary);
}

/* 分类导航 */
.category-nav {
    padding: 20px;
}

.category-item {
    margin-bottom: 8px;
}

.category-link {
    display: flex;
    align-items: center;
    padding: 10px 12px;
    color: var(--text-secondary);
    text-decoration: none;
    border-radius: calc(var(--border-radius) - 2px);
    transition: var(--transition);
    font-size: 0.9rem;
}

.category-link:hover,
.category-link.active {
    background: var(--primary-color);
    color: white;
}

.category-icon {
    margin-right: 8px;
    font-size: 1rem;
}

.category-count {
    margin-left: auto;
    background: var(--background-color);
    color: var(--text-secondary);
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
}

.category-link.active .category-count {
    background: rgba(255, 255, 255, 0.2);
    color: white;
}

/* 快速访问 */
.quick-access {
    padding: 20px;
    border-top: 1px solid var(--border-color);
}

.quick-access h4 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 15px;
}

.quick-links {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.quick-link {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    background: none;
    border: 1px solid var(--border-color);
    border-radius: calc(var(--border-radius) - 2px);
    cursor: pointer;
    font-size: 0.875rem;
    color: var(--text-secondary);
    transition: var(--transition);
}

.quick-link:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.count {
    background: var(--background-color);
    color: var(--text-secondary);
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 0.75rem;
    font-weight: 500;
}

/* 内容区域 */
.content-area {
    background: var(--surface-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    min-height: 600px;
}

/* 欢迎页面 */
.welcome-page {
    padding: 40px;
    text-align: center;
}

.welcome-content h2 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 16px;
}

.welcome-content p {
    font-size: 1.125rem;
    color: var(--text-secondary);
    margin-bottom: 40px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

/* 功能卡片 */
.feature-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 24px;
    margin-bottom: 40px;
}

.feature-card {
    padding: 24px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.feature-card:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-md);
}

.feature-icon {
    font-size: 2.5rem;
    margin-bottom: 16px;
}

.feature-card h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 8px;
}

.feature-card p {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* 快速开始按钮 */
.quick-start {
    margin-top: 40px;
}

.quick-start h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 24px;
}

.quick-start-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    justify-content: center;
}

/* 按钮样式 */
.btn {
    padding: 12px 24px;
    border: none;
    border-radius: var(--border-radius);
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: var(--primary-hover);
}

.btn-outline {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-outline:hover {
    background: var(--primary-color);
    color: white;
}

/* 响应式设计 */
@media (max-width: 768px) {
    :root {
        --sidebar-width: 100%;
    }
    
    .content-wrapper {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .sidebar {
        position: relative;
        top: auto;
        max-height: none;
    }
    
    .toggle-btn {
        display: block;
    }
    
    .category-nav {
        display: none;
    }
    
    .category-nav.show {
        display: block;
    }
    
    .header-content {
        flex-direction: column;
        gap: 15px;
    }
    
    .search-container {
        margin-top: 0;
        width: 100%;
    }
    
    .welcome-page {
        padding: 20px;
    }
    
    .welcome-content h2 {
        font-size: 1.5rem;
    }
    
    .feature-cards {
        grid-template-columns: 1fr;
        gap: 16px;
    }
    
    .quick-start-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .btn {
        width: 100%;
        max-width: 300px;
        justify-content: center;
    }
}

/* FAQ页面样式 */
.faq-page {
    padding: 30px;
}

.breadcrumb {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 24px;
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.breadcrumb a {
    color: var(--primary-color);
    text-decoration: none;
}

.breadcrumb a:hover {
    text-decoration: underline;
}

.breadcrumb-separator {
    color: var(--text-secondary);
}

/* FAQ内容 */
.faq-content {
    margin-bottom: 40px;
}

.faq-header {
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid var(--border-color);
}

.faq-id {
    display: inline-block;
    background: var(--primary-color);
    color: white;
    padding: 4px 12px;
    border-radius: 16px;
    font-size: 0.75rem;
    font-weight: 600;
    margin-bottom: 12px;
}

.faq-title {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--text-primary);
    line-height: 1.3;
}

.faq-priority {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
    margin-left: 12px;
}

.priority-high {
    background: #fef2f2;
    color: var(--danger-color);
}

.priority-medium {
    background: #fef3c7;
    color: var(--warning-color);
}

.priority-low {
    background: #f0f9ff;
    color: var(--primary-color);
}

.faq-body {
    font-size: 1rem;
    line-height: 1.7;
    color: var(--text-primary);
}

.faq-body h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 24px 0 12px 0;
}

.faq-body h4 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 20px 0 10px 0;
}

.faq-body ul, .faq-body ol {
    margin: 16px 0;
    padding-left: 24px;
}

.faq-body li {
    margin-bottom: 8px;
}

.faq-body table {
    width: 100%;
    border-collapse: collapse;
    margin: 20px 0;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    overflow: hidden;
}

.faq-body th,
.faq-body td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.faq-body th {
    background: var(--background-color);
    font-weight: 600;
    color: var(--text-primary);
}

.faq-body tr:last-child td {
    border-bottom: none;
}

.warning-box {
    background: #fef3c7;
    border: 1px solid #f59e0b;
    border-radius: var(--border-radius);
    padding: 16px;
    margin: 20px 0;
}

.warning-box::before {
    content: "⚠️ ";
    font-weight: bold;
}

.info-box {
    background: #f0f9ff;
    border: 1px solid var(--primary-color);
    border-radius: var(--border-radius);
    padding: 16px;
    margin: 20px 0;
}

.info-box::before {
    content: "ℹ️ ";
    font-weight: bold;
}

/* 相关问题 */
.related-questions {
    margin-top: 40px;
    padding-top: 24px;
    border-top: 1px solid var(--border-color);
}

.related-questions h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 16px;
}

.related-list {
    display: grid;
    gap: 12px;
}

.related-item {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    background: var(--background-color);
    border-radius: var(--border-radius);
    text-decoration: none;
    color: var(--text-primary);
    transition: var(--transition);
}

.related-item:hover {
    background: var(--primary-color);
    color: white;
}

.related-item-id {
    background: var(--surface-color);
    color: var(--primary-color);
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    margin-right: 12px;
    min-width: 60px;
    text-align: center;
}

.related-item:hover .related-item-id {
    background: rgba(255, 255, 255, 0.2);
    color: white;
}

/* 操作按钮 */
.action-buttons {
    display: flex;
    gap: 12px;
    margin-top: 30px;
    padding-top: 24px;
    border-top: 1px solid var(--border-color);
}

.action-buttons .btn {
    flex: 1;
    justify-content: center;
}

/* 搜索结果页面 */
.search-page {
    padding: 30px;
}

.search-results-header {
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid var(--border-color);
}

.search-results-header h2 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 8px;
}

.search-info {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.search-results {
    display: grid;
    gap: 16px;
}

.search-result-item {
    padding: 20px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    transition: var(--transition);
    cursor: pointer;
}

.search-result-item:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-md);
}

.search-result-header {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
}

.search-result-id {
    background: var(--primary-color);
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    margin-right: 12px;
}

.search-result-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    flex: 1;
}

.search-result-category {
    background: var(--background-color);
    color: var(--text-secondary);
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
}

.search-result-excerpt {
    color: var(--text-secondary);
    font-size: 0.9rem;
    line-height: 1.5;
    margin-top: 8px;
}

.search-highlight {
    background: #fef3c7;
    color: var(--warning-color);
    font-weight: 600;
    padding: 1px 2px;
    border-radius: 2px;
}

/* 分类页面 */
.category-page {
    padding: 30px;
}

.category-header {
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid var(--border-color);
}

.category-header h2 {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 12px;
}

.category-description {
    color: var(--text-secondary);
    font-size: 1rem;
    line-height: 1.6;
}

.category-questions {
    display: grid;
    gap: 16px;
}

.question-item {
    padding: 20px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    transition: var(--transition);
    cursor: pointer;
}

.question-item:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-md);
}

.question-header {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
}

.question-id {
    background: var(--primary-color);
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    margin-right: 12px;
}

.question-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    flex: 1;
}

.question-priority {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
}

.question-summary {
    color: var(--text-secondary);
    font-size: 0.9rem;
    line-height: 1.5;
    margin-top: 8px;
}

/* 底部 */
.footer {
    background: var(--text-primary);
    color: white;
    padding: 40px 0 20px;
    margin-top: 60px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
}

.footer-section h4 {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 16px;
}

.footer-section p,
.footer-section a {
    color: #cbd5e1;
    text-decoration: none;
    margin-bottom: 8px;
    display: block;
}

.footer-section a:hover {
    color: white;
}

.copyright {
    text-align: center;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #374151;
    color: #9ca3af;
}

/* 返回顶部按钮 */
.back-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 50%;
    font-size: 1.2rem;
    cursor: pointer;
    box-shadow: var(--shadow-lg);
    transition: var(--transition);
    opacity: 0;
    visibility: hidden;
}

.back-to-top.show {
    opacity: 1;
    visibility: visible;
}

.back-to-top:hover {
    background: var(--primary-hover);
    transform: translateY(-2px);
}

/* 加载提示 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
}

.loading-overlay.show {
    opacity: 1;
    visibility: visible;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--border-color);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }

    .welcome-page {
        padding: 15px;
    }

    .feature-card {
        padding: 16px;
    }

    .language-switcher {
        width: 100%;
        justify-content: center;
    }

    .faq-page,
    .search-page,
    .category-page {
        padding: 20px;
    }

    .action-buttons {
        flex-direction: column;
    }

    .back-to-top {
        bottom: 20px;
        right: 20px;
        width: 45px;
        height: 45px;
    }
}
