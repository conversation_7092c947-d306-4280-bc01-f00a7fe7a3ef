<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GoMyHire 司机FAQ | Driver FAQ | FAQ Pemandu</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="brand-theme.css">
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🚗</text></svg>">
</head>
<body>
    <!-- 顶部导航栏 -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <div class="logo-container">
                        <div class="logo-icon">
                            <svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <rect width="40" height="40" rx="8" fill="#1a73e8"/>
                                <path d="M8 20C8 16.6863 10.6863 14 14 14H26C29.3137 14 32 16.6863 32 20V24C32 27.3137 29.3137 30 26 30H14C10.6863 30 8 27.3137 8 24V20Z" fill="white"/>
                                <circle cx="12" cy="26" r="2" fill="#1a73e8"/>
                                <circle cx="28" cy="26" r="2" fill="#1a73e8"/>
                                <path d="M14 18H26V22H14V18Z" fill="#1a73e8"/>
                                <path d="M18 10L20 14H16L18 10Z" fill="#34a853"/>
                            </svg>
                        </div>
                        <div class="logo-text">
                            <h1>GoMyHire</h1>
                            <span class="logo-subtitle">司机FAQ系统</span>
                        </div>
                    </div>
                </div>
                
                <!-- 语言切换 -->
                <div class="language-switcher">
                    <button class="lang-btn active" data-lang="zh">中文</button>
                    <button class="lang-btn" data-lang="en">English</button>
                    <button class="lang-btn" data-lang="ms">Bahasa</button>
                </div>
            </div>
            
            <!-- 搜索栏 -->
            <div class="search-container">
                <input type="text" id="searchInput" placeholder="搜索问题编号或关键词..." class="search-input">
                <button id="searchBtn" class="search-btn">🔍</button>
            </div>
        </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="main-content">
        <div class="container">
            <div class="content-wrapper">
                <!-- 侧边栏导航 -->
                <aside class="sidebar">
                    <div class="sidebar-header">
                        <h3 data-i18n="categories">问题分类</h3>
                        <button id="toggleSidebar" class="toggle-btn">☰</button>
                    </div>
                    
                    <nav class="category-nav" id="categoryNav">
                        <!-- 分类导航将通过JavaScript动态生成 -->
                    </nav>
                    
                    <!-- 快速访问 -->
                    <div class="quick-access">
                        <h4 data-i18n="quickAccess">快速访问</h4>
                        <div class="quick-links">
                            <button class="quick-link" data-action="favorites">
                                <span data-i18n="favorites">收藏夹</span> <span class="count" id="favCount">0</span>
                            </button>
                            <button class="quick-link" data-action="recent">
                                <span data-i18n="recent">最近浏览</span> <span class="count" id="recentCount">0</span>
                            </button>
                            <button class="quick-link" data-action="popular">
                                <span data-i18n="popular">热门问题</span>
                            </button>
                        </div>
                    </div>
                </aside>

                <!-- 主内容区 -->
                <section class="content-area">
                    <!-- 欢迎页面 -->
                    <div id="welcomePage" class="welcome-page">
                        <div class="welcome-content">
                            <h2 data-i18n="welcomeTitle">欢迎使用GoMyHire司机FAQ系统</h2>
                            <p data-i18n="welcomeDesc">这里汇集了司机朋友们最常遇到的问题和详细解答。您可以通过分类浏览或搜索快速找到需要的信息。</p>
                            
                            <div class="feature-cards">
                                <div class="feature-card">
                                    <div class="feature-icon">🔍</div>
                                    <h3 data-i18n="searchFeature">智能搜索</h3>
                                    <p data-i18n="searchFeatureDesc">支持问题编号、关键词搜索</p>
                                </div>
                                <div class="feature-card">
                                    <div class="feature-icon">📱</div>
                                    <h3 data-i18n="mobileFeature">移动优化</h3>
                                    <p data-i18n="mobileFeatureDesc">完美适配手机和平板设备</p>
                                </div>
                                <div class="feature-card">
                                    <div class="feature-icon">🌐</div>
                                    <h3 data-i18n="multiLangFeature">多语言支持</h3>
                                    <p data-i18n="multiLangFeatureDesc">中文、英文、马来文三语切换</p>
                                </div>
                            </div>
                            
                            <div class="quick-start">
                                <h3 data-i18n="quickStart">快速开始</h3>
                                <div class="quick-start-buttons">
                                    <button class="btn btn-brand brand-shine" data-category="registration">
                                        <span class="brand-icon">🚗</span>
                                        <span data-i18n="registration">司机注册</span>
                                    </button>
                                    <button class="btn btn-brand brand-shine" data-category="app-usage">
                                        <span class="brand-icon">📱</span>
                                        <span data-i18n="appUsage">APP使用</span>
                                    </button>
                                    <button class="btn btn-brand brand-shine" data-category="order-management">
                                        <span class="brand-icon">📋</span>
                                        <span data-i18n="orderManagement">订单管理</span>
                                    </button>
                                    <button class="btn btn-brand brand-shine" data-category="emergency">
                                        <span class="brand-icon">🚨</span>
                                        <span data-i18n="emergency">紧急处理</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- FAQ详情页面 -->
                    <div id="faqPage" class="faq-page page-hidden">
                        <!-- 面包屑导航 -->
                        <nav class="breadcrumb" id="breadcrumb">
                            <!-- 面包屑将通过JavaScript动态生成 -->
                        </nav>

                        <!-- FAQ内容 -->
                        <article class="faq-content" id="faqContent">
                            <!-- FAQ内容将通过JavaScript动态加载 -->
                        </article>

                        <!-- 相关问题 -->
                        <section class="related-questions" id="relatedQuestions">
                            <!-- 相关问题将通过JavaScript动态生成 -->
                        </section>

                        <!-- 操作按钮 -->
                        <div class="action-buttons">
                            <button id="favoriteBtn" class="btn btn-outline" type="button">
                                <span class="icon">⭐</span>
                                <span data-i18n="addToFavorites">添加到收藏</span>
                            </button>
                            <button id="shareBtn" class="btn btn-outline" type="button">
                                <span class="icon">📤</span>
                                <span data-i18n="share">分享</span>
                            </button>
                            <button id="feedbackBtn" class="btn btn-outline" type="button">
                                <span class="icon">💬</span>
                                <span data-i18n="feedback">反馈</span>
                            </button>
                        </div>
                    </div>

                    <!-- 搜索结果页面 -->
                    <div id="searchPage" class="search-page page-hidden">
                        <div class="search-results-header">
                            <h2 data-i18n="searchResults">搜索结果</h2>
                            <div class="search-info" id="searchInfo"></div>
                        </div>
                        <div class="search-results" id="searchResults">
                            <!-- 搜索结果将通过JavaScript动态生成 -->
                        </div>
                    </div>

                    <!-- 分类列表页面 -->
                    <div id="categoryPage" class="category-page page-hidden">
                        <div class="category-header">
                            <h2 id="categoryTitle"></h2>
                            <div class="category-description" id="categoryDescription"></div>
                        </div>
                        <div class="category-questions" id="categoryQuestions">
                            <!-- 分类问题列表将通过JavaScript动态生成 -->
                        </div>
                    </div>
                </section>
            </div>
        </div>
    </main>

    <!-- 底部信息 -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4 data-i18n="contact">联系我们</h4>
                    <p data-i18n="customerService">客服热线：400-XXX-XXXX</p>
                    <p data-i18n="email">邮箱：<EMAIL></p>
                </div>
                <div class="footer-section">
                    <h4 data-i18n="quickLinks">快速链接</h4>
                    <a href="#" data-i18n="driverApp">司机APP下载</a>
                    <a href="#" data-i18n="driverPortal">司机门户</a>
                    <a href="#" data-i18n="training">培训中心</a>
                </div>
                <div class="footer-section">
                    <p class="copyright">
                        © 2025 GoMyHire. <span data-i18n="allRightsReserved">版权所有</span>
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- 返回顶部按钮 -->
    <button id="backToTop" class="back-to-top" title="返回顶部">↑</button>

    <!-- 加载提示 -->
    <div id="loadingOverlay" class="loading-overlay">
        <div class="loading-spinner"></div>
        <p data-i18n="loading">加载中...</p>
    </div>

    <!-- JavaScript文件 -->
    <script src="data.js"></script>
    <script src="i18n.js"></script>
    <script src="app.js"></script>
</body>
</html>
