// FAQ数据结构
const faqData = {
    // 分类定义
    categories: {
        "registration": {
            id: "registration",
            icon: "🚗",
            name: {
                zh: "司机注册与认证",
                en: "Driver Registration & Verification", 
                ms: "Pendaftaran & Pengesahan Pemandu"
            },
            description: {
                zh: "司机注册流程、材料准备、审核时间等相关问题",
                en: "Driver registration process, document preparation, review time and related issues",
                ms: "Proses pendaftaran pemandu, penyed<PERSON>an dokumen, masa semakan dan isu berkaitan"
            },
            priority: 1
        },
        "app-usage": {
            id: "app-usage",
            icon: "📱",
            name: {
                zh: "APP使用与技术支持",
                en: "App Usage & Technical Support",
                ms: "Penggunaan Aplikasi & Sokongan Teknikal"
            },
            description: {
                zh: "APP登录、更新、功能使用、技术故障等问题",
                en: "App login, updates, feature usage, technical issues",
                ms: "Log masuk aplikasi, kemas kini, penggunaan ciri, isu teknikal"
            },
            priority: 2
        },
        "order-management": {
            id: "order-management",
            icon: "📋",
            name: {
                zh: "订单管理与调度",
                en: "Order Management & Dispatch",
                ms: "Pengurusan Pesanan & Penghantaran"
            },
            description: {
                zh: "订单接收、取消、状态更新、调度相关问题",
                en: "Order receiving, cancellation, status updates, dispatch related issues",
                ms: "Penerimaan pesanan, pembatalan, kemas kini status, isu berkaitan penghantaran"
            },
            priority: 3
        },
        "customer-service": {
            id: "customer-service",
            icon: "💬",
            name: {
                zh: "乘客沟通与服务",
                en: "Customer Communication & Service",
                ms: "Komunikasi & Perkhidmatan Pelanggan"
            },
            description: {
                zh: "与乘客沟通、服务标准、特殊需求处理等",
                en: "Customer communication, service standards, special needs handling",
                ms: "Komunikasi pelanggan, standard perkhidmatan, pengendalian keperluan khas"
            },
            priority: 4
        },
        "payment": {
            id: "payment",
            icon: "💰",
            name: {
                zh: "支付与财务管理",
                en: "Payment & Financial Management",
                ms: "Pembayaran & Pengurusan Kewangan"
            },
            description: {
                zh: "收入查询、提现、费用结算、账单问题等",
                en: "Income inquiry, withdrawal, fee settlement, billing issues",
                ms: "Pertanyaan pendapatan, pengeluaran, penyelesaian yuran, isu bil"
            },
            priority: 5
        },
        "emergency": {
            id: "emergency",
            icon: "🚨",
            name: {
                zh: "应急处理",
                en: "Emergency Handling",
                ms: "Pengendalian Kecemasan"
            },
            description: {
                zh: "紧急情况处理、事故报告、安全问题等",
                en: "Emergency handling, accident reporting, safety issues",
                ms: "Pengendalian kecemasan, laporan kemalangan, isu keselamatan"
            },
            priority: 6
        }
    },
    
    // FAQ问题数据
    questions: [
        // 司机注册与认证
        {
            id: "FC-RG-01",
            category: "registration",
            priority: "high",
            title: {
                zh: "如何注册成为GoMyHire司机？",
                en: "How to register as a GoMyHire driver?",
                ms: "Bagaimana untuk mendaftar sebagai pemandu GoMyHire?"
            },
            content: {
                zh: `
                    <h3>详细步骤：</h3>
                    <ol>
                        <li><strong>下载应用</strong>：在应用商店搜索"GoMyHire Driver"下载官方应用</li>
                        <li><strong>填写资料</strong>：
                            <ul>
                                <li>个人信息：姓名、身份证号、联系方式</li>
                                <li>车辆信息：车牌号、车型、车龄、座位数</li>
                                <li>驾驶证信息：驾驶证号、准驾车型、有效期限</li>
                            </ul>
                        </li>
                        <li><strong>背景调查</strong>：上传无犯罪记录证明、交通违法记录</li>
                        <li><strong>提交审核</strong>：确认信息无误后提交，系统将在1-3个工作日内完成审核</li>
                    </ol>
                    
                    <div class="warning-box">
                        <strong>注意事项：</strong>
                        <ul>
                            <li>确保所有证件有效期至少3个月以上</li>
                            <li>照片需清晰、无反光、无遮挡</li>
                            <li>信息填写错误可能导致审核延迟</li>
                        </ul>
                    </div>
                `,
                en: `
                    <h3>Detailed Steps:</h3>
                    <ol>
                        <li><strong>Download App</strong>: Search for "GoMyHire Driver" in app store and download the official app</li>
                        <li><strong>Fill Information</strong>:
                            <ul>
                                <li>Personal info: Name, ID number, contact details</li>
                                <li>Vehicle info: License plate, model, age, seating capacity</li>
                                <li>License info: License number, vehicle type, expiry date</li>
                            </ul>
                        </li>
                        <li><strong>Background Check</strong>: Upload criminal record clearance and traffic violation records</li>
                        <li><strong>Submit for Review</strong>: Confirm all information is correct and submit. System will complete review within 1-3 business days</li>
                    </ol>
                    
                    <div class="warning-box">
                        <strong>Important Notes:</strong>
                        <ul>
                            <li>Ensure all documents are valid for at least 3 months</li>
                            <li>Photos must be clear, without glare or obstruction</li>
                            <li>Incorrect information may cause review delays</li>
                        </ul>
                    </div>
                `,
                ms: `
                    <h3>Langkah Terperinci:</h3>
                    <ol>
                        <li><strong>Muat Turun Aplikasi</strong>: Cari "GoMyHire Driver" di kedai aplikasi dan muat turun aplikasi rasmi</li>
                        <li><strong>Isi Maklumat</strong>:
                            <ul>
                                <li>Maklumat peribadi: Nama, nombor IC, butiran hubungan</li>
                                <li>Maklumat kenderaan: Nombor plat, model, umur, kapasiti tempat duduk</li>
                                <li>Maklumat lesen: Nombor lesen, jenis kenderaan, tarikh tamat tempoh</li>
                            </ul>
                        </li>
                        <li><strong>Pemeriksaan Latar Belakang</strong>: Muat naik sijil bersih rekod jenayah dan rekod pelanggaran lalu lintas</li>
                        <li><strong>Hantar untuk Semakan</strong>: Sahkan semua maklumat betul dan hantar. Sistem akan selesaikan semakan dalam 1-3 hari bekerja</li>
                    </ol>
                    
                    <div class="warning-box">
                        <strong>Nota Penting:</strong>
                        <ul>
                            <li>Pastikan semua dokumen sah untuk sekurang-kurangnya 3 bulan</li>
                            <li>Gambar mestilah jelas, tanpa silau atau halangan</li>
                            <li>Maklumat yang salah boleh menyebabkan kelewatan semakan</li>
                        </ul>
                    </div>
                `
            },
            tags: ["注册", "registration", "pendaftaran", "新司机", "new driver", "pemandu baru"],
            relatedQuestions: ["FC-RG-02", "FC-SH-01", "FC-SH-02"]
        },
        
        {
            id: "FC-RG-02",
            category: "registration",
            priority: "high",
            title: {
                zh: "注册时需要准备哪些材料？",
                en: "What documents are needed for registration?",
                ms: "Dokumen apa yang diperlukan untuk pendaftaran?"
            },
            content: {
                zh: `
                    <h3>必备材料清单：</h3>
                    <table>
                        <thead>
                            <tr>
                                <th>材料类型</th>
                                <th>具体要求</th>
                                <th>备注</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>身份证件</td>
                                <td>二代身份证正反面</td>
                                <td>需在有效期内</td>
                            </tr>
                            <tr>
                                <td>驾驶证</td>
                                <td>C1及以上，驾龄≥1年</td>
                                <td>正副页齐全</td>
                            </tr>
                            <tr>
                                <td>车辆行驶证</td>
                                <td>车辆所有人为本人或直系亲属</td>
                                <td>需在检验有效期内</td>
                            </tr>
                            <tr>
                                <td>车辆保险</td>
                                <td>交强险+商业险(三者≥50万)</td>
                                <td>保单需在有效期内</td>
                            </tr>
                            <tr>
                                <td>个人照片</td>
                                <td>近期免冠白底证件照</td>
                                <td>建议穿正装</td>
                            </tr>
                            <tr>
                                <td>车辆照片</td>
                                <td>前后左右45°角各一张</td>
                                <td>清晰显示车牌</td>
                            </tr>
                        </tbody>
                    </table>
                    
                    <h4>补充材料（如有）：</h4>
                    <ul>
                        <li>居住证明（非本地户籍）</li>
                        <li>车辆购置发票（新车）</li>
                        <li>维修保养记录</li>
                    </ul>
                `,
                en: `
                    <h3>Required Documents Checklist:</h3>
                    <table>
                        <thead>
                            <tr>
                                <th>Document Type</th>
                                <th>Requirements</th>
                                <th>Notes</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>ID Document</td>
                                <td>Both sides of national ID</td>
                                <td>Must be valid</td>
                            </tr>
                            <tr>
                                <td>Driving License</td>
                                <td>Class C1 or above, ≥1 year experience</td>
                                <td>Complete pages required</td>
                            </tr>
                            <tr>
                                <td>Vehicle Registration</td>
                                <td>Owner must be applicant or immediate family</td>
                                <td>Must be within inspection validity</td>
                            </tr>
                            <tr>
                                <td>Vehicle Insurance</td>
                                <td>Compulsory + Commercial (≥500k coverage)</td>
                                <td>Policy must be valid</td>
                            </tr>
                            <tr>
                                <td>Personal Photo</td>
                                <td>Recent passport-style photo, white background</td>
                                <td>Formal attire recommended</td>
                            </tr>
                            <tr>
                                <td>Vehicle Photos</td>
                                <td>Front, back, left, right at 45° angles</td>
                                <td>License plate clearly visible</td>
                            </tr>
                        </tbody>
                    </table>
                    
                    <h4>Additional Documents (if applicable):</h4>
                    <ul>
                        <li>Residence proof (for non-local residents)</li>
                        <li>Vehicle purchase invoice (for new cars)</li>
                        <li>Maintenance records</li>
                    </ul>
                `,
                ms: `
                    <h3>Senarai Dokumen Diperlukan:</h3>
                    <table>
                        <thead>
                            <tr>
                                <th>Jenis Dokumen</th>
                                <th>Keperluan</th>
                                <th>Nota</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Dokumen Pengenalan</td>
                                <td>Kedua-dua belah IC negara</td>
                                <td>Mesti sah</td>
                            </tr>
                            <tr>
                                <td>Lesen Memandu</td>
                                <td>Kelas C1 atau ke atas, ≥1 tahun pengalaman</td>
                                <td>Halaman lengkap diperlukan</td>
                            </tr>
                            <tr>
                                <td>Pendaftaran Kenderaan</td>
                                <td>Pemilik mestilah pemohon atau keluarga terdekat</td>
                                <td>Mesti dalam tempoh sah pemeriksaan</td>
                            </tr>
                            <tr>
                                <td>Insurans Kenderaan</td>
                                <td>Wajib + Komersial (≥500k perlindungan)</td>
                                <td>Polisi mesti sah</td>
                            </tr>
                            <tr>
                                <td>Gambar Peribadi</td>
                                <td>Gambar gaya pasport terkini, latar putih</td>
                                <td>Pakaian formal disyorkan</td>
                            </tr>
                            <tr>
                                <td>Gambar Kenderaan</td>
                                <td>Hadapan, belakang, kiri, kanan pada sudut 45°</td>
                                <td>Nombor plat jelas kelihatan</td>
                            </tr>
                        </tbody>
                    </table>
                    
                    <h4>Dokumen Tambahan (jika berkenaan):</h4>
                    <ul>
                        <li>Bukti kediaman (untuk penduduk bukan tempatan)</li>
                        <li>Invois pembelian kenderaan (untuk kereta baru)</li>
                        <li>Rekod penyelenggaraan</li>
                    </ul>
                `
            },
            tags: ["材料", "documents", "dokumen", "证件", "requirements", "keperluan"],
            relatedQuestions: ["FC-RG-01", "FC-SH-01"]
        },
        
        // APP使用与技术支持
        {
            id: "AS-LY-01",
            category: "app-usage",
            priority: "high",
            title: {
                zh: "无法登录或遇到系统更新导致的异常？",
                en: "Unable to login or experiencing system update issues?",
                ms: "Tidak dapat log masuk atau mengalami isu kemas kini sistem?"
            },
            content: {
                zh: `
                    <h3>解决步骤：</h3>
                    <ol>
                        <li><strong>步骤1</strong>：请确认网络连接正常，并尝试重启APP</li>
                        <li><strong>步骤2</strong>：检查是否处于系统更新期，如是，请稍候几分钟后重试</li>
                        <li><strong>步骤3</strong>：如问题依旧，请联系客户服务，同时提供截图或错误提示以便快速诊断</li>
                    </ol>
                    
                    <div class="info-box">
                        <strong>常见原因：</strong>
                        <ul>
                            <li>网络连接不稳定</li>
                            <li>APP版本过旧需要更新</li>
                            <li>系统维护期间暂时无法访问</li>
                            <li>账号被临时锁定</li>
                        </ul>
                    </div>
                    
                    <h4>预防措施：</h4>
                    <ul>
                        <li>定期更新APP到最新版本</li>
                        <li>确保设备有足够存储空间</li>
                        <li>关注官方公告了解维护时间</li>
                    </ul>
                `,
                en: `
                    <h3>Solution Steps:</h3>
                    <ol>
                        <li><strong>Step 1</strong>: Confirm network connection is stable and try restarting the app</li>
                        <li><strong>Step 2</strong>: Check if system is under maintenance, if so, wait a few minutes and retry</li>
                        <li><strong>Step 3</strong>: If problem persists, contact customer service with screenshots or error messages for quick diagnosis</li>
                    </ol>
                    
                    <div class="info-box">
                        <strong>Common Causes:</strong>
                        <ul>
                            <li>Unstable network connection</li>
                            <li>Outdated app version requiring update</li>
                            <li>Temporary inaccessibility during system maintenance</li>
                            <li>Account temporarily locked</li>
                        </ul>
                    </div>
                    
                    <h4>Prevention Measures:</h4>
                    <ul>
                        <li>Regularly update app to latest version</li>
                        <li>Ensure device has sufficient storage space</li>
                        <li>Follow official announcements for maintenance schedules</li>
                    </ul>
                `,
                ms: `
                    <h3>Langkah Penyelesaian:</h3>
                    <ol>
                        <li><strong>Langkah 1</strong>: Sahkan sambungan rangkaian stabil dan cuba mulakan semula aplikasi</li>
                        <li><strong>Langkah 2</strong>: Periksa jika sistem sedang dalam penyelenggaraan, jika ya, tunggu beberapa minit dan cuba lagi</li>
                        <li><strong>Langkah 3</strong>: Jika masalah berterusan, hubungi perkhidmatan pelanggan dengan tangkapan skrin atau mesej ralat untuk diagnosis pantas</li>
                    </ol>
                    
                    <div class="info-box">
                        <strong>Punca Biasa:</strong>
                        <ul>
                            <li>Sambungan rangkaian tidak stabil</li>
                            <li>Versi aplikasi lama memerlukan kemas kini</li>
                            <li>Tidak boleh diakses sementara semasa penyelenggaraan sistem</li>
                            <li>Akaun dikunci sementara</li>
                        </ul>
                    </div>
                    
                    <h4>Langkah Pencegahan:</h4>
                    <ul>
                        <li>Kemas kini aplikasi secara berkala ke versi terkini</li>
                        <li>Pastikan peranti mempunyai ruang storan yang mencukupi</li>
                        <li>Ikuti pengumuman rasmi untuk jadual penyelenggaraan</li>
                    </ul>
                `
            },
            tags: ["登录", "login", "log masuk", "系统", "system", "sistem", "更新", "update", "kemas kini"],
            relatedQuestions: ["AS-DX-01", "AS-YY-01"]
        },

        // 订单管理与调度
        {
            id: "DD-QX-01",
            category: "order-management",
            priority: "high",
            title: {
                zh: "不小心点了'接受'但无法执行任务，如何取消？",
                en: "Accidentally clicked 'Accept' but cannot execute the task, how to cancel?",
                ms: "Secara tidak sengaja klik 'Terima' tetapi tidak dapat melaksanakan tugas, bagaimana untuk membatalkan?"
            },
            content: {
                zh: `
                    <h3>紧急处理步骤：</h3>
                    <ol>
                        <li><strong>步骤1</strong>：请立即联系在线客服，并提供订单号及取消原因，客服将核实后决定是否无责取消</li>
                        <li><strong>步骤2</strong>：明确说明是误操作或时间冲突，客服会根据情况协助取消</li>
                        <li><strong>步骤3</strong>：请避免频繁误触，以免影响评分和未来接单资格</li>
                    </ol>

                    <div class="warning-box">
                        <strong>重要提醒：</strong>
                        <ul>
                            <li>误操作取消不会扣分，但需要客服确认</li>
                            <li>频繁取消会影响司机评级</li>
                            <li>建议仔细查看订单详情后再接受</li>
                        </ul>
                    </div>

                    <h4>预防措施：</h4>
                    <ul>
                        <li>接单前仔细检查时间和路线</li>
                        <li>确认自己的可用时间</li>
                        <li>避免在驾驶时操作手机</li>
                    </ul>
                `,
                en: `
                    <h3>Emergency Handling Steps:</h3>
                    <ol>
                        <li><strong>Step 1</strong>: Immediately contact online customer service, provide order number and cancellation reason, customer service will verify and decide whether to cancel without penalty</li>
                        <li><strong>Step 2</strong>: Clearly explain it was a misoperation or time conflict, customer service will assist with cancellation based on the situation</li>
                        <li><strong>Step 3</strong>: Please avoid frequent misoperations to prevent affecting ratings and future order eligibility</li>
                    </ol>

                    <div class="warning-box">
                        <strong>Important Reminder:</strong>
                        <ul>
                            <li>Misoperation cancellations won't deduct points, but require customer service confirmation</li>
                            <li>Frequent cancellations will affect driver rating</li>
                            <li>Recommend carefully reviewing order details before accepting</li>
                        </ul>
                    </div>

                    <h4>Prevention Measures:</h4>
                    <ul>
                        <li>Carefully check time and route before accepting orders</li>
                        <li>Confirm your available time</li>
                        <li>Avoid operating phone while driving</li>
                    </ul>
                `,
                ms: `
                    <h3>Langkah Pengendalian Kecemasan:</h3>
                    <ol>
                        <li><strong>Langkah 1</strong>: Segera hubungi perkhidmatan pelanggan dalam talian, berikan nombor pesanan dan sebab pembatalan, perkhidmatan pelanggan akan mengesahkan dan memutuskan sama ada untuk membatalkan tanpa penalti</li>
                        <li><strong>Langkah 2</strong>: Jelaskan dengan jelas bahawa ia adalah salah operasi atau konflik masa, perkhidmatan pelanggan akan membantu dengan pembatalan berdasarkan situasi</li>
                        <li><strong>Langkah 3</strong>: Sila elakkan salah operasi yang kerap untuk mengelakkan menjejaskan penilaian dan kelayakan pesanan masa depan</li>
                    </ol>

                    <div class="warning-box">
                        <strong>Peringatan Penting:</strong>
                        <ul>
                            <li>Pembatalan salah operasi tidak akan menolak mata, tetapi memerlukan pengesahan perkhidmatan pelanggan</li>
                            <li>Pembatalan yang kerap akan menjejaskan penilaian pemandu</li>
                            <li>Disyorkan untuk menyemak butiran pesanan dengan teliti sebelum menerima</li>
                        </ul>
                    </div>

                    <h4>Langkah Pencegahan:</h4>
                    <ul>
                        <li>Periksa masa dan laluan dengan teliti sebelum menerima pesanan</li>
                        <li>Sahkan masa yang tersedia</li>
                        <li>Elakkan mengendalikan telefon semasa memandu</li>
                    </ul>
                `
            },
            tags: ["取消订单", "cancel order", "batal pesanan", "误操作", "misoperation", "salah operasi"],
            relatedQuestions: ["DD-CX-01", "DD-SW-01"]
        },

        {
            id: "AS-DX-01",
            category: "app-usage",
            priority: "high",
            title: {
                zh: "为什么携程订单在携程司机端App里无法显示？",
                en: "Why don't Ctrip orders show up in the Ctrip Driver App?",
                ms: "Mengapa pesanan Ctrip tidak muncul dalam Aplikasi Pemandu Ctrip?"
            },
            content: {
                zh: `
                    <h3>解决步骤：</h3>
                    <ol>
                        <li><strong>步骤1</strong>：请刷新APP页面</li>
                        <li><strong>步骤2</strong>：确认已更新至最新版本</li>
                        <li><strong>步骤3</strong>：如问题仍未解决，请提供订单ID给客服进行核查</li>
                    </ol>

                    <div class="info-box">
                        <strong>订单同步机制：</strong>
                        <p>携程订单通常需要1-2小时才会同步到携程司机端APP。如果超过2小时仍未显示，请联系客服处理。</p>
                    </div>

                    <h4>常见原因：</h4>
                    <ul>
                        <li>系统同步延迟</li>
                        <li>APP版本过旧</li>
                        <li>网络连接问题</li>
                        <li>订单状态异常</li>
                    </ul>

                    <h4>检查清单：</h4>
                    <ul>
                        <li>确认网络连接稳定</li>
                        <li>检查APP是否为最新版本</li>
                        <li>尝试退出并重新登录</li>
                        <li>清除APP缓存后重试</li>
                    </ul>
                `,
                en: `
                    <h3>Solution Steps:</h3>
                    <ol>
                        <li><strong>Step 1</strong>: Please refresh the app page</li>
                        <li><strong>Step 2</strong>: Confirm you have updated to the latest version</li>
                        <li><strong>Step 3</strong>: If the problem persists, provide the order ID to customer service for verification</li>
                    </ol>

                    <div class="info-box">
                        <strong>Order Sync Mechanism:</strong>
                        <p>Ctrip orders usually take 1-2 hours to sync to the Ctrip Driver App. If it still doesn't show after 2 hours, please contact customer service.</p>
                    </div>

                    <h4>Common Causes:</h4>
                    <ul>
                        <li>System sync delay</li>
                        <li>Outdated app version</li>
                        <li>Network connection issues</li>
                        <li>Abnormal order status</li>
                    </ul>

                    <h4>Checklist:</h4>
                    <ul>
                        <li>Confirm stable network connection</li>
                        <li>Check if app is the latest version</li>
                        <li>Try logging out and back in</li>
                        <li>Clear app cache and retry</li>
                    </ul>
                `,
                ms: `
                    <h3>Langkah Penyelesaian:</h3>
                    <ol>
                        <li><strong>Langkah 1</strong>: Sila segarkan halaman aplikasi</li>
                        <li><strong>Langkah 2</strong>: Sahkan anda telah mengemas kini ke versi terkini</li>
                        <li><strong>Langkah 3</strong>: Jika masalah berterusan, berikan ID pesanan kepada perkhidmatan pelanggan untuk pengesahan</li>
                    </ol>

                    <div class="info-box">
                        <strong>Mekanisme Segerak Pesanan:</strong>
                        <p>Pesanan Ctrip biasanya mengambil masa 1-2 jam untuk disegerakkan ke Aplikasi Pemandu Ctrip. Jika masih tidak muncul selepas 2 jam, sila hubungi perkhidmatan pelanggan.</p>
                    </div>

                    <h4>Punca Biasa:</h4>
                    <ul>
                        <li>Kelewatan segerak sistem</li>
                        <li>Versi aplikasi lama</li>
                        <li>Isu sambungan rangkaian</li>
                        <li>Status pesanan tidak normal</li>
                    </ul>

                    <h4>Senarai Semak:</h4>
                    <ul>
                        <li>Sahkan sambungan rangkaian stabil</li>
                        <li>Periksa jika aplikasi adalah versi terkini</li>
                        <li>Cuba log keluar dan masuk semula</li>
                        <li>Kosongkan cache aplikasi dan cuba lagi</li>
                    </ul>
                `
            },
            tags: ["携程", "ctrip", "订单显示", "order display", "paparan pesanan", "同步", "sync", "segerak"],
            relatedQuestions: ["AS-LY-01", "DD-DX-01"]
        },

        {
            id: "GK-YW-01",
            category: "customer-service",
            priority: "high",
            title: {
                zh: "客户航班延误或在移民局排队时间过长怎么办？",
                en: "What to do when customer's flight is delayed or immigration queue is too long?",
                ms: "Apa yang perlu dilakukan apabila penerbangan pelanggan lewat atau barisan imigresen terlalu panjang?"
            },
            content: {
                zh: `
                    <h3>处理步骤：</h3>
                    <ol>
                        <li><strong>步骤1</strong>：向客服报告航班延误及实际到达时间</li>
                        <li><strong>步骤2</strong>：在允许的免费等待时间内，现场拍照（含GPS时间戳）并上传给客服，以证明等待情况</li>
                        <li><strong>步骤3</strong>：若等待超时且乘客未出现，请与客服沟通办理离场或额外费用事宜</li>
                    </ol>

                    <div class="info-box">
                        <strong>等待时间标准：</strong>
                        <ul>
                            <li>接机服务：免费等待90分钟</li>
                            <li>送机服务：免费等待30分钟</li>
                            <li>超时部分按标准费率计费</li>
                        </ul>
                    </div>

                    <h4>拍照要求：</h4>
                    <ul>
                        <li>使用GPS相机拍摄，确保有时间和位置信息</li>
                        <li>拍摄到达大厅或指定等待区域</li>
                        <li>包含车牌号码在照片中</li>
                        <li>每30分钟拍照一次作为等待证明</li>
                    </ul>

                    <h4>沟通技巧：</h4>
                    <ul>
                        <li>主动联系乘客了解情况</li>
                        <li>通过Live Chat功能保持沟通</li>
                        <li>及时向客服汇报最新情况</li>
                        <li>保持耐心和专业态度</li>
                    </ul>
                `,
                en: `
                    <h3>Handling Steps:</h3>
                    <ol>
                        <li><strong>Step 1</strong>: Report flight delay and actual arrival time to customer service</li>
                        <li><strong>Step 2</strong>: Within the allowed free waiting time, take photos on-site (with GPS timestamp) and upload to customer service to prove waiting situation</li>
                        <li><strong>Step 3</strong>: If waiting exceeds time limit and passenger doesn't appear, communicate with customer service about departure or additional fees</li>
                    </ol>

                    <div class="info-box">
                        <strong>Waiting Time Standards:</strong>
                        <ul>
                            <li>Airport pickup service: 90 minutes free waiting</li>
                            <li>Airport drop-off service: 30 minutes free waiting</li>
                            <li>Overtime charged at standard rates</li>
                        </ul>
                    </div>

                    <h4>Photo Requirements:</h4>
                    <ul>
                        <li>Use GPS camera to ensure time and location information</li>
                        <li>Photograph arrival hall or designated waiting area</li>
                        <li>Include license plate number in photos</li>
                        <li>Take photos every 30 minutes as waiting proof</li>
                    </ul>

                    <h4>Communication Tips:</h4>
                    <ul>
                        <li>Proactively contact passengers to understand the situation</li>
                        <li>Maintain communication through Live Chat function</li>
                        <li>Report latest updates to customer service promptly</li>
                        <li>Maintain patience and professional attitude</li>
                    </ul>
                `,
                ms: `
                    <h3>Langkah Pengendalian:</h3>
                    <ol>
                        <li><strong>Langkah 1</strong>: Laporkan kelewatan penerbangan dan masa ketibaan sebenar kepada perkhidmatan pelanggan</li>
                        <li><strong>Langkah 2</strong>: Dalam masa menunggu percuma yang dibenarkan, ambil gambar di lokasi (dengan cap masa GPS) dan muat naik kepada perkhidmatan pelanggan untuk membuktikan situasi menunggu</li>
                        <li><strong>Langkah 3</strong>: Jika menunggu melebihi had masa dan penumpang tidak muncul, berkomunikasi dengan perkhidmatan pelanggan tentang berlepas atau yuran tambahan</li>
                    </ol>

                    <div class="info-box">
                        <strong>Standard Masa Menunggu:</strong>
                        <ul>
                            <li>Perkhidmatan jemput lapangan terbang: 90 minit menunggu percuma</li>
                            <li>Perkhidmatan hantar lapangan terbang: 30 minit menunggu percuma</li>
                            <li>Masa lebih dikenakan pada kadar standard</li>
                        </ul>
                    </div>

                    <h4>Keperluan Gambar:</h4>
                    <ul>
                        <li>Gunakan kamera GPS untuk memastikan maklumat masa dan lokasi</li>
                        <li>Gambar dewan ketibaan atau kawasan menunggu yang ditetapkan</li>
                        <li>Sertakan nombor plat dalam gambar</li>
                        <li>Ambil gambar setiap 30 minit sebagai bukti menunggu</li>
                    </ul>

                    <h4>Petua Komunikasi:</h4>
                    <ul>
                        <li>Hubungi penumpang secara proaktif untuk memahami situasi</li>
                        <li>Kekalkan komunikasi melalui fungsi Live Chat</li>
                        <li>Laporkan kemas kini terkini kepada perkhidmatan pelanggan dengan segera</li>
                        <li>Kekalkan kesabaran dan sikap profesional</li>
                    </ul>
                `
            },
            tags: ["航班延误", "flight delay", "kelewatan penerbangan", "等待", "waiting", "menunggu", "拍照证明", "photo proof", "bukti gambar"],
            relatedQuestions: ["GK-TX-01", "PG-FY-01"]
        },

        {
            id: "PG-FY-01",
            category: "payment",
            priority: "medium",
            title: {
                zh: "如何申请'Paging费'RM20？",
                en: "How to apply for 'Paging fee' RM20?",
                ms: "Bagaimana untuk memohon 'Yuran Paging' RM20?"
            },
            content: {
                zh: `
                    <h3>申请条件：</h3>
                    <p><strong>前提：客服要求举牌</strong></p>

                    <h3>申请步骤：</h3>
                    <ol>
                        <li><strong>步骤1</strong>：出发前请查看订单备注，确认是否需举牌</li>
                        <li><strong>步骤2</strong>：举牌后，请在Live Chat上传照片或与乘客合影留证</li>
                        <li><strong>步骤3</strong>：将订单号提供给客服，以便添加RM20补助</li>
                    </ol>

                    <div class="info-box">
                        <strong>举牌服务说明：</strong>
                        <p>Meet and Greet服务是指司机在机场到达大厅举牌迎接客户，并协助客户从机场出口离开的服务。</p>
                    </div>

                    <h4>举牌要求：</h4>
                    <ul>
                        <li>使用纸张写上欢迎客户的字样</li>
                        <li>在到达大厅明显位置举牌</li>
                        <li>协助客户携带行李到车辆</li>
                        <li>引导客户从机场出口离开</li>
                    </ul>

                    <h4>拍照要求：</h4>
                    <ul>
                        <li>举牌照片（显示客户姓名）</li>
                        <li>与客户合影（如客户同意）</li>
                        <li>GPS时间戳照片</li>
                        <li>上传至Live Chat作为证明</li>
                    </ul>

                    <div class="warning-box">
                        <strong>注意：</strong>如果无法举牌或客户自行找到司机，可按客服要求当场退还RM20并拍照证明。公司会将对应金额返还给司机。
                    </div>
                `,
                en: `
                    <h3>Application Requirements:</h3>
                    <p><strong>Prerequisite: Customer service requires paging</strong></p>

                    <h3>Application Steps:</h3>
                    <ol>
                        <li><strong>Step 1</strong>: Check order notes before departure to confirm if paging is required</li>
                        <li><strong>Step 2</strong>: After paging, upload photos in Live Chat or take photos with passengers as proof</li>
                        <li><strong>Step 3</strong>: Provide order number to customer service to add RM20 subsidy</li>
                    </ol>

                    <div class="info-box">
                        <strong>Paging Service Description:</strong>
                        <p>Meet and Greet service means the driver holds a sign to welcome customers at the airport arrival hall and assists customers to leave from the airport exit.</p>
                    </div>

                    <h4>Paging Requirements:</h4>
                    <ul>
                        <li>Use paper to write welcome message for customer</li>
                        <li>Hold sign in prominent position at arrival hall</li>
                        <li>Assist customers with luggage to vehicle</li>
                        <li>Guide customers to leave from airport exit</li>
                    </ul>

                    <h4>Photo Requirements:</h4>
                    <ul>
                        <li>Paging photo (showing customer name)</li>
                        <li>Photo with customer (if customer agrees)</li>
                        <li>GPS timestamp photo</li>
                        <li>Upload to Live Chat as proof</li>
                    </ul>

                    <div class="warning-box">
                        <strong>Note:</strong> If unable to page or customer finds driver themselves, can return RM20 on-site as required by customer service and take photo proof. Company will return corresponding amount to driver.
                    </div>
                `,
                ms: `
                    <h3>Keperluan Permohonan:</h3>
                    <p><strong>Prasyarat: Perkhidmatan pelanggan memerlukan paging</strong></p>

                    <h3>Langkah Permohonan:</h3>
                    <ol>
                        <li><strong>Langkah 1</strong>: Periksa nota pesanan sebelum berlepas untuk mengesahkan jika paging diperlukan</li>
                        <li><strong>Langkah 2</strong>: Selepas paging, muat naik gambar dalam Live Chat atau ambil gambar dengan penumpang sebagai bukti</li>
                        <li><strong>Langkah 3</strong>: Berikan nombor pesanan kepada perkhidmatan pelanggan untuk menambah subsidi RM20</li>
                    </ol>

                    <div class="info-box">
                        <strong>Penerangan Perkhidmatan Paging:</strong>
                        <p>Perkhidmatan Meet and Greet bermaksud pemandu memegang papan tanda untuk mengalu-alukan pelanggan di dewan ketibaan lapangan terbang dan membantu pelanggan keluar dari pintu keluar lapangan terbang.</p>
                    </div>

                    <h4>Keperluan Paging:</h4>
                    <ul>
                        <li>Gunakan kertas untuk menulis mesej selamat datang untuk pelanggan</li>
                        <li>Pegang papan tanda di kedudukan yang menonjol di dewan ketibaan</li>
                        <li>Bantu pelanggan dengan bagasi ke kenderaan</li>
                        <li>Pandu pelanggan untuk keluar dari pintu keluar lapangan terbang</li>
                    </ul>

                    <h4>Keperluan Gambar:</h4>
                    <ul>
                        <li>Gambar paging (menunjukkan nama pelanggan)</li>
                        <li>Gambar dengan pelanggan (jika pelanggan bersetuju)</li>
                        <li>Gambar cap masa GPS</li>
                        <li>Muat naik ke Live Chat sebagai bukti</li>
                    </ul>

                    <div class="warning-box">
                        <strong>Nota:</strong> Jika tidak dapat melakukan paging atau pelanggan menemui pemandu sendiri, boleh memulangkan RM20 di tempat seperti yang diperlukan oleh perkhidmatan pelanggan dan ambil gambar bukti. Syarikat akan memulangkan jumlah yang sepadan kepada pemandu.
                    </div>
                `
            },
            tags: ["paging", "举牌", "meet and greet", "RM20", "费用申请", "fee application", "permohonan yuran"],
            relatedQuestions: ["GK-YW-01", "GK-TX-01"]
        }
    ]
};

// 数据管理器
class DataManager {
    constructor() {
        this.data = faqData;
        this.searchIndex = this.buildSearchIndex();
    }
    
    // 构建搜索索引
    buildSearchIndex() {
        const index = [];
        this.data.questions.forEach(question => {
            // 为每种语言建立索引
            Object.keys(question.title).forEach(lang => {
                index.push({
                    id: question.id,
                    category: question.category,
                    priority: question.priority,
                    lang: lang,
                    title: question.title[lang],
                    content: this.stripHtml(question.content[lang]),
                    tags: question.tags || []
                });
            });
        });
        return index;
    }
    
    // 移除HTML标签
    stripHtml(html) {
        const div = document.createElement('div');
        div.innerHTML = html;
        return div.textContent || div.innerText || '';
    }
    
    // 获取所有分类
    getCategories() {
        return this.data.categories;
    }
    
    // 根据分类获取问题
    getQuestionsByCategory(categoryId) {
        return this.data.questions.filter(q => q.category === categoryId);
    }
    
    // 根据ID获取问题
    getQuestionById(id) {
        return this.data.questions.find(q => q.id === id);
    }
    
    // 搜索问题
    searchQuestions(query, lang = 'zh') {
        if (!query.trim()) return [];
        
        const searchTerm = query.toLowerCase();
        const results = [];
        
        this.searchIndex.forEach(item => {
            if (item.lang !== lang) return;
            
            let score = 0;
            
            // 问题ID完全匹配
            if (item.id.toLowerCase() === searchTerm) {
                score += 100;
            } else if (item.id.toLowerCase().includes(searchTerm)) {
                score += 50;
            }
            
            // 标题匹配
            if (item.title.toLowerCase().includes(searchTerm)) {
                score += 30;
            }
            
            // 内容匹配
            if (item.content.toLowerCase().includes(searchTerm)) {
                score += 10;
            }
            
            // 标签匹配
            item.tags.forEach(tag => {
                if (tag.toLowerCase().includes(searchTerm)) {
                    score += 20;
                }
            });
            
            if (score > 0) {
                results.push({
                    ...item,
                    score: score,
                    question: this.getQuestionById(item.id)
                });
            }
        });
        
        // 按分数排序
        return results.sort((a, b) => b.score - a.score);
    }
    
    // 获取相关问题
    getRelatedQuestions(questionId) {
        const question = this.getQuestionById(questionId);
        if (!question || !question.relatedQuestions) return [];
        
        return question.relatedQuestions
            .map(id => this.getQuestionById(id))
            .filter(q => q !== undefined);
    }
    
    // 获取热门问题
    getPopularQuestions(limit = 10) {
        return this.data.questions
            .filter(q => q.priority === 'high')
            .slice(0, limit);
    }
}

// 导出数据管理器
window.DataManager = DataManager;
